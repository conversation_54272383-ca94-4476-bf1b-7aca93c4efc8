import { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useMemoizedFn, useResponsive } from 'ahooks';
import { useShiyong<PERSON><PERSON><PERSON> } from '../minganbuju/zhutitiqigong.js';
import <PERSON><PERSON><PERSON><PERSON><PERSON>an from '../minganbuju/zujian_zhutiqiehuan.jsx';

// 导航栏容器
const Da<PERSON><PERSON><PERSON><PERSON><PERSON> = styled(motion.nav)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
    ? 'rgba(0, 0, 0, 0.95)'
    : props.theme.yanse.biaomian
  };
  backdrop-filter: blur(15px);
  border-bottom: 1px solid;
  border-bottom-color: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
    ? props.theme.yanse.danjinse_touming
    : props.theme.yanse.danlanse_touming
  };
  transition: background ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun},
              backdrop-filter ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun},
              border-bottom-color ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
`;

// 导航栏内容容器
const Daohanglanneirongrq = styled.div`
  width: 100%;
  padding: 0 ${props => props.theme.jianju.zhongdeng};
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  @media (max-width: 768px) {
    justify-content: space-between;
    padding: 0 ${props => props.theme.jianju.xiao};
    height: 56px;
  }
`;

// Logo 区域
const Logoququyu = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.xiao};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 只在非触摸设备上显示hover效果 */
  @media (hover: hover) and (pointer: fine) {
    &:hover {
      opacity: 0.8;
    }
  }
`;

// Logo 图片
const Logotupian = styled.img`
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  object-fit: cover;

  @media (max-width: 768px) {
    width: 28px;
    height: 28px;
  }
`;

// 网站名称
const Wangzhanmingcheng = styled.h1`
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0;
  white-space: nowrap;

  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }

  @media (max-width: 480px) {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 桌面端菜单容器
const Zhuomianduancaidan = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.zhongdeng};
  margin-left: ${props => props.theme.jianju.da};

  @media (max-width: 768px) {
    display: none;
  }
`;

// 菜单项
const Caidanxiang = styled(motion.a)`
  position: relative;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  text-decoration: none;
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.theme.yanse.beijing_er};
    border-radius: ${props => props.theme.yuanjiao.xiao};
    opacity: 0;
    transform: scale(0.8);
    transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
    z-index: -1;
  }

  &:hover {
    color: ${props => props.theme.yanse.wenzi_zhuyao};

    &::before {
      opacity: 1;
      transform: scale(1);
    }
  }

  &.active {
    color: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
      ? props.theme.yanse.danjinse
      : props.theme.yanse.danlanse
    };
    cursor: default !important;
    opacity: 0.6;
    pointer-events: none;

    &::before {
      opacity: 1;
      transform: scale(1);
      background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
        ? `linear-gradient(135deg,
            ${props.theme.yanse.danjinse}15 0%,
            ${props.theme.yanse.danjinse}25 50%,
            ${props.theme.yanse.danjinse}15 100%)`
        : `linear-gradient(135deg,
            ${props.theme.yanse.danlanse}15 0%,
            ${props.theme.yanse.danlanse}25 50%,
            ${props.theme.yanse.danlanse}15 100%)`
      };
    }

    /* 禁用hover效果 */
    &:hover {
      transform: none !important;
      opacity: 0.6 !important;
    }
  }
`;

// 弹性空间
const Tanxingkongjian = styled.div`
  flex-grow: 1;
`;

// 右侧操作区域
const Youcecaozuoqu = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.zhongdeng};
`;


// 桌面端主题切换容器
const Zhuomianduanzhutiqiehuan = styled.div`
  @media (max-width: 768px) {
    display: none;
  }
`;

// 移动端菜单按钮
const Yidongduancaidananniu = styled(motion.button)`
  display: none;
  background: none;
  border: none;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  cursor: pointer;
  padding: ${props => props.theme.jianju.xiao};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  &:hover {
    background: ${props => props.theme.yanse.beijing_er};
  }

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

// 移动端侧边栏
const Yidongduancebiankuang = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 50vw;
  height: 100vh;
  background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
    ? 'rgba(0, 0, 0, 0.98)'
    : props.theme.yanse.biaomian
  };
  backdrop-filter: blur(20px);
  border-right: 1px solid ${props => props.theme.yanse.biankuang};
  box-shadow: ${props => props.theme.yinying.da};
  z-index: 1001;
  padding: ${props => props.theme.jianju.da};
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  /* 屏蔽手机端点击高亮效果 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  @media (max-width: 480px) {
    width: 60vw;
  }
`;

// 移动端菜单头部
const Yidongduancaidantoubu = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${props => props.theme.jianju.da};
  padding-bottom: ${props => props.theme.jianju.zhongdeng};
  border-bottom: 1px solid ${props => props.theme.yanse.biankuang};
`;


// 移动端菜单列表
const Yidongduancaidanliebiao = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.jianju.xiao};
  flex: 1;
`;

// 移动端菜单项
const Yidongduancaidanxiang = styled(motion.a)`
  color: ${props => props.theme.yanse.wenzi_ciyao};
  text-decoration: none;
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  padding: ${props => props.theme.jianju.zhongdeng};
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  position: relative;
  overflow: hidden;

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 渐变背景效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    border-radius: ${props => props.theme.yuanjiao.zhongdeng};
    opacity: 0;
    transform: scale(0.8);
    transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
    z-index: -1;
  }

  &:hover {
    color: ${props => props.theme.yanse.wenzi_zhuyao};

    &::before {
      opacity: 0.5;
      transform: scale(1);
      background: ${props => props.theme.yanse.beijing_er};
    }
  }

  &.active {
    color: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
      ? props.theme.yanse.danjinse
      : props.theme.yanse.danlanse
    };
    cursor: default !important;
    opacity: 0.6;
    pointer-events: none;

    &::before {
      opacity: 1;
      transform: scale(1);
      background: ${props => props.theme.yanse.wenzi_zhuyao === '#ffffff'
        ? `linear-gradient(135deg,
            ${props.theme.yanse.danjinse}15 0%,
            ${props.theme.yanse.danjinse}25 50%,
            ${props.theme.yanse.danjinse}15 100%)`
        : `linear-gradient(135deg,
            ${props.theme.yanse.danlanse}15 0%,
            ${props.theme.yanse.danlanse}25 50%,
            ${props.theme.yanse.danlanse}15 100%)`
      };
    }

    /* 禁用hover效果 */
    &:hover {
      transform: none !important;
      opacity: 0.6 !important;

      &::before {
        opacity: 1 !important;
      }
    }
  }
`;

// 移动端底部操作区域
const Yidongduandibucaozuoqu = styled.div`
  margin-top: auto;
  padding-top: ${props => props.theme.jianju.zhongdeng};
  border-top: 1px solid ${props => props.theme.yanse.biankuang};
`;

// 移动端登录链接
const Yidongduandenglulianjie = styled(motion.a)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.xiao};
  padding: ${props => props.theme.jianju.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  text-decoration: none;
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  border: 1px solid ${props => props.theme.yanse.biankuang};
  background: ${props => props.theme.yanse.biaomian};
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  cursor: pointer;
  margin-bottom: 0;

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 只在非触摸设备上显示hover效果 */
  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background: ${props => props.theme.yanse.beijing_er};
      border-color: ${props => props.theme.yanse.biankuang_qian};
      transform: translateX(4px);
    }
  }
`;

// 移动端主题切换区域
const Yidongduanzhutiqiehuanqu = styled.div`
  padding-top: 5px;
`;

// 主题切换选项
const Zhutiqiehuanxuanxiang = styled(motion.div)`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.jianju.zhongdeng};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};

  /* 完全禁用焦点功能 */
  pointer-events: auto;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 强制移除所有聚焦和激活状态 */
  &:focus,
  &:focus-visible,
  &:focus-within,
  &:active,
  &[tabindex],
  &[tabindex="0"],
  &[tabindex="-1"] {
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
  }

  /* 移动端强制处理 */
  @media (hover: none) and (pointer: coarse) {
    &:focus,
    &:active,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
      transform: none !important;
    }
  }

  /* 只在非触摸设备上显示hover效果 */
  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background: ${props => props.theme.yanse.beijing_er};
    }
  }
`;

// 主题切换文字
const Zhutiqiehuanwenzi = styled.span`
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
`;

// 主题切换图标
const Zhutiqiehuantubiao = styled(motion.div)`
  width: 20px;
  height: 20px;
  margin-right: ${props => props.theme.jianju.xiao};
  color: ${props => props.$isanhei
    ? props.theme.yanse.danjinse
    : props.theme.yanse.danlanse
  };
  transition: color ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};

  svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
  }
`;

// 遮罩层
const Zhezhaoceng = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(2px);
`;

// 动画配置
const cebiankuangdonghhua = {
  hidden: {
    x: '-100%',
    boxShadow: 'none',
    borderRightColor: 'transparent',
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  },
  visible: {
    x: 0,
    transition: {
      duration: 0.3,
      ease: 'easeInOut'
    }
  }
};

const zhezhaocengdonghhua = {
  hidden: {
    opacity: 0,
    transition: {
      duration: 0.4,
      ease: 'easeInOut'
    }
  },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: 'easeInOut'
    }
  }
};

// 登录图标组件
const Denglutubiao = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
    <motion.path
      d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
    />
    <motion.path
      d="M10 17l5-5-5-5"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, delay: 0.2, ease: "easeInOut" }}
    />
    <motion.path
      d="M15 12H3"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, delay: 0.4, ease: "easeInOut" }}
    />
  </svg>
);

// 汉堡菜单图标组件
const Hanbaobaocaidantubiao = ({ isopen }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <motion.path
      d={isopen ? "M6 18L18 6" : "M3 12h18"}
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      animate={{ d: isopen ? "M6 18L18 6" : "M3 12h18" }}
      transition={{ duration: 0.2 }}
    />
    <motion.path
      d={isopen ? "M6 6l12 12" : "M3 6h18"}
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      animate={{ 
        d: isopen ? "M6 6l12 12" : "M3 6h18",
        opacity: isopen ? 1 : 1
      }}
      transition={{ duration: 0.2 }}
    />
    <motion.path
      d="M3 18h18"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      animate={{ opacity: isopen ? 0 : 1 }}
      transition={{ duration: 0.2 }}
    />
  </svg>
);

/**
 * 顶部导航栏组件
 * @param {string} wangzhanmingcheng - 网站名称
 * @param {string} wangzhanlogo - 网站Logo URL
 * @param {Array} caidanxiangmu - 菜单项数组 [{mingcheng: '首页', lianjie: '/', huoyue: true}]
 * @param {boolean} xianshi - 是否显示导航栏
 */
const Dingbudaohanglan = ({
  wangzhanmingcheng = '网站名称',
  wangzhanlogo = '',
  caidanxiangmu = [],
  xianshi = true
}) => {
  const [cebiankuangkaiqii, shezhi_cebiankuangkaiqii] = useState(false);
  const { dangqianzhuti, qiehuandaoxiayigezhuti } = useShiyongzhuti();
  const responsive = useResponsive();

  // 判断是否为暗黑主题
  const shifoianheizuti = dangqianzhuti === 'anhei';

  // 构建完整的菜单项数组，包含内置的首页链接
  const wanzheng_caidanxiangmu = useMemo(() => {
    // 检查当前页面是否为首页
    const dangqian_lujing = window.location.pathname;
    const shouye_huoyue = dangqian_lujing === '/' || dangqian_lujing === '';

    // 创建首页菜单项
    const shouye_xiangmu = {
      mingcheng: '首页',
      lianjie: '/',
      huoyue: shouye_huoyue
    };

    // 将首页菜单项放在最前面，然后是外部传入的菜单项
    return [shouye_xiangmu, ...caidanxiangmu];
  }, [caidanxiangmu]);

  // 构建桌面端菜单项数组，包含登录链接
  const zhuomian_caidanxiangmu = useMemo(() => {
    // 检查当前页面是否为登录页
    const dangqian_lujing = window.location.pathname;
    const denglu_huoyue = dangqian_lujing === '/denglu';

    // 创建登录/注册菜单项（仅用于桌面端）
    const denglu_xiangmu = {
      mingcheng: '登录/注册',
      lianjie: '/denglu',
      huoyue: denglu_huoyue
    };

    // 桌面端菜单包含所有项目，包括登录链接
    return [...wanzheng_caidanxiangmu, denglu_xiangmu];
  }, [wanzheng_caidanxiangmu]);

  // 处理菜单项点击
  const chuli_caidanxiang_dianji = useMemoizedFn((lianjie, event, shifoihuoyue = false) => {
    // 如果是活跃菜单项，阻止所有导航行为
    if (shifoihuoyue) {
      event.preventDefault();
      event.stopPropagation();
      // 移除焦点状态
      if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
        try {
          event.currentTarget.blur();
        } catch (e) {
          // 忽略blur错误
        }
      }
      return;
    }

    if (lianjie) {
      // 检查是否是当前页面，如果是则强制刷新
      if (window.location.href === lianjie || window.location.pathname === lianjie) {
        window.location.reload();
      } else {
        window.location.href = lianjie;
      }
    }
    shezhi_cebiankuangkaiqii(false);
    // 移除焦点状态，防止手机端持续聚焦
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 检测是否为移动端设备
  const jiance_yidongduan = useMemoizedFn(() => {
    // 检测触摸设备
    const shifoichudian = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    // 检测屏幕宽度
    const shifouzhaixiaomu = window.innerWidth <= 768;
    // 检测用户代理
    const yonghudaili = navigator.userAgent.toLowerCase();
    const shifoiyidongduan = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(yonghudaili);

    return shifoichudian && (shifouzhaixiaomu || shifoiyidongduan);
  });

  // 处理logo点击
  const chuli_logo_dianji = useMemoizedFn((event) => {
    // 在移动端环境下阻止页面刷新
    if (jiance_yidongduan()) {
      event.preventDefault();
      event.stopPropagation();
      // 移除焦点状态
      if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
        try {
          event.currentTarget.blur();
        } catch (e) {
          // 忽略blur错误
        }
      }
      return;
    }

    // 桌面端保持原有行为：强制刷新到首页
    window.location.href = '/';
    // 移除焦点状态
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 切换侧边栏
  const qiehuan_cebiankuang = useMemoizedFn((event) => {
    shezhi_cebiankuangkaiqii(!cebiankuangkaiqii);
    // 移除焦点状态，防止手机端持续聚焦
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 关闭侧边栏
  const guanbi_cebiankuang = useMemoizedFn(() => {
    shezhi_cebiankuangkaiqii(false);
  });

  // 处理主题切换
  const chuli_zhutiqiehuan = useMemoizedFn((event) => {
    qiehuandaoxiayigezhuti();
    // 移除焦点状态，防止手机端持续聚焦
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      try {
        event.currentTarget.blur();
      } catch (e) {
        // 忽略blur错误
      }
    }
  });

  // 处理触摸结束，强制移除焦点
  const chuli_chumojieshu = useMemoizedFn((event) => {
    if (event && event.currentTarget && typeof event.currentTarget.blur === 'function') {
      setTimeout(() => {
        try {
          if (event.currentTarget && typeof event.currentTarget.blur === 'function') {
            event.currentTarget.blur();
          }
        } catch (e) {
          // 忽略blur错误
        }
      }, 100);
    }
  });

  // 监听窗口大小变化，自动关闭移动端菜单
  useEffect(() => {
    if (responsive?.md && cebiankuangkaiqii) {
      shezhi_cebiankuangkaiqii(false);
    }
  }, [responsive?.md, cebiankuangkaiqii]);

  // 阻止页面滚动（当侧边栏打开时）
  useEffect(() => {
    if (cebiankuangkaiqii) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [cebiankuangkaiqii]);

  if (!xianshi) {
    return null;
  }

  return (
    <>
      <Daohanglanrongqi
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
      >
        <Daohanglanneirongrq>
          {/* Logo 区域 */}
          <Logoququyu
            onClick={(e) => chuli_logo_dianji(e)}
            onTouchEnd={chuli_chumojieshu}
            onTouchStart={(e) => e.preventDefault()}
            onMouseDown={(e) => e.preventDefault()}
            tabIndex={-1}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {wangzhanlogo && (
              <Logotupian src={wangzhanlogo} alt={wangzhanmingcheng} />
            )}
            <Wangzhanmingcheng>{wangzhanmingcheng}</Wangzhanmingcheng>
          </Logoququyu>

          {/* 桌面端菜单 */}
          <Zhuomianduancaidan>
            {zhuomian_caidanxiangmu.map((xiangmu, suoyin) => (
              <Caidanxiang
                key={suoyin}
                className={xiangmu.huoyue ? 'active' : ''}
                onClick={(e) => chuli_caidanxiang_dianji(xiangmu.lianjie, e, xiangmu.huoyue)}
                whileHover={xiangmu.huoyue ? {} : { scale: 1.05 }}
                whileTap={xiangmu.huoyue ? {} : { scale: 0.95 }}
                style={{
                  cursor: xiangmu.huoyue ? 'default' : 'pointer',
                  opacity: xiangmu.huoyue ? 0.6 : 1
                }}
              >
                {xiangmu.mingcheng}
              </Caidanxiang>
            ))}
          </Zhuomianduancaidan>

          <Tanxingkongjian />

          {/* 右侧操作区域 */}
          <Youcecaozuoqu>
            {/* 桌面端主题切换 */}
            <Zhuomianduanzhutiqiehuan>
              <ZhutiqiehuanZujian size={40} />
            </Zhuomianduanzhutiqiehuan>

            {/* 移动端菜单按钮 */}
            <Yidongduancaidananniu
              onClick={(e) => qiehuan_cebiankuang(e)}
              onTouchEnd={chuli_chumojieshu}
              onTouchStart={(e) => e.preventDefault()}
              onMouseDown={(e) => e.preventDefault()}
              tabIndex={-1}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Hanbaobaocaidantubiao isopen={cebiankuangkaiqii} />
            </Yidongduancaidananniu>
          </Youcecaozuoqu>
        </Daohanglanneirongrq>
      </Daohanglanrongqi>

      {/* 移动端侧边栏和遮罩 */}
      <AnimatePresence mode="wait">
        {cebiankuangkaiqii && (
          <>
            <Zhezhaoceng
              variants={zhezhaocengdonghhua}
              initial="hidden"
              animate="visible"
              exit="hidden"
              onClick={guanbi_cebiankuang}
            />
            <Yidongduancebiankuang
              variants={cebiankuangdonghhua}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              <Yidongduancaidantoubu>
                <Logoququyu
                  onClick={(e) => chuli_logo_dianji(e)}
                  onTouchEnd={chuli_chumojieshu}
                  onTouchStart={(e) => e.preventDefault()}
                  onMouseDown={(e) => e.preventDefault()}
                  tabIndex={-1}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {wangzhanlogo && (
                    <Logotupian src={wangzhanlogo} alt={wangzhanmingcheng} />
                  )}
                  <Wangzhanmingcheng style={{ display: 'block' }}>
                    {wangzhanmingcheng}
                  </Wangzhanmingcheng>
                </Logoququyu>
              </Yidongduancaidantoubu>

              <Yidongduancaidanliebiao>
                {wanzheng_caidanxiangmu.map((xiangmu, suoyin) => (
                  <Yidongduancaidanxiang
                    key={suoyin}
                    className={xiangmu.huoyue ? 'active' : ''}
                    onClick={(e) => chuli_caidanxiang_dianji(xiangmu.lianjie, e, xiangmu.huoyue)}
                    onTouchEnd={chuli_chumojieshu}
                    onTouchStart={(e) => e.preventDefault()}
                    onMouseDown={(e) => e.preventDefault()}
                    tabIndex={-1}
                    whileHover={xiangmu.huoyue ? {} : { x: 8 }}
                    whileTap={xiangmu.huoyue ? {} : { scale: 0.98 }}
                    style={{
                      cursor: xiangmu.huoyue ? 'default' : 'pointer',
                      opacity: xiangmu.huoyue ? 0.6 : 1
                    }}
                  >
                    {xiangmu.mingcheng}
                  </Yidongduancaidanxiang>
                ))}
              </Yidongduancaidanliebiao>

              {/* 移动端底部操作区域 */}
              <Yidongduandibucaozuoqu>
                {/* 移动端登录链接 */}
                <Yidongduandenglulianjie
                  href="/denglu"
                  onTouchEnd={chuli_chumojieshu}
                  onTouchStart={(e) => e.preventDefault()}
                  onMouseDown={(e) => e.preventDefault()}
                  tabIndex={-1}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Denglutubiao />
                  登录/注册
                </Yidongduandenglulianjie>

                {/* 移动端主题切换区域 */}
                <Yidongduanzhutiqiehuanqu>
                <Zhutiqiehuanxuanxiang
                  onClick={(e) => chuli_zhutiqiehuan(e)}
                  onTouchEnd={chuli_chumojieshu}
                  onTouchStart={(e) => e.preventDefault()}
                  onMouseDown={(e) => e.preventDefault()}
                  tabIndex={-1}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Zhutiqiehuantubiao
                    $isanhei={shifoianheizuti}
                    key={shifoianheizuti ? 'moon' : 'sun'}
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    exit={{ scale: 0, rotate: 180 }}
                    transition={{
                      duration: 0.3,
                      ease: "easeInOut",
                      scale: { type: "spring", stiffness: 200, damping: 15 }
                    }}
                  >
                    {shifoianheizuti ? (
                      // 月亮图标（深色模式）
                      <motion.svg
                        viewBox="0 0 24 24"
                        fill="none"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.1 }}
                      >
                        <motion.path
                          d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
                          fill="currentColor"
                          initial={{ pathLength: 0, opacity: 0 }}
                          animate={{ pathLength: 1, opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.6, ease: "easeInOut" }}
                        />
                      </motion.svg>
                    ) : (
                      // 太阳图标（浅色模式）
                      <motion.svg
                        viewBox="0 0 24 24"
                        fill="none"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.1 }}
                      >
                        <motion.circle
                          cx="12"
                          cy="12"
                          r="5"
                          fill="currentColor"
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.6, ease: "easeInOut" }}
                        />
                        {/* 太阳光线动画 */}
                        {[
                          { x1: "12", y1: "1", x2: "12", y2: "3" },
                          { x1: "12", y1: "21", x2: "12", y2: "23" },
                          { x1: "4.22", y1: "4.22", x2: "5.64", y2: "5.64" },
                          { x1: "18.36", y1: "18.36", x2: "19.78", y2: "19.78" },
                          { x1: "1", y1: "12", x2: "3", y2: "12" },
                          { x1: "21", y1: "12", x2: "23", y2: "12" },
                          { x1: "4.22", y1: "19.78", x2: "5.64", y2: "18.36" },
                          { x1: "18.36", y1: "5.64", x2: "19.78", y2: "4.22" }
                        ].map((line, index) => (
                          <motion.line
                            key={index}
                            x1={line.x1}
                            y1={line.y1}
                            x2={line.x2}
                            y2={line.y2}
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            initial={{ pathLength: 0, opacity: 0 }}
                            animate={{ pathLength: 1, opacity: 1 }}
                            transition={{
                              duration: 0.5,
                              delay: 0.8 + index * 0.1,
                              ease: "easeInOut"
                            }}
                          />
                        ))}
                      </motion.svg>
                    )}
                  </Zhutiqiehuantubiao>
                  <Zhutiqiehuanwenzi>
                    {shifoianheizuti ? '浅色模式' : '深色模式'}
                  </Zhutiqiehuanwenzi>
                </Zhutiqiehuanxuanxiang>
                </Yidongduanzhutiqiehuanqu>
              </Yidongduandibucaozuoqu>
            </Yidongduancebiankuang>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default Dingbudaohanglan;
